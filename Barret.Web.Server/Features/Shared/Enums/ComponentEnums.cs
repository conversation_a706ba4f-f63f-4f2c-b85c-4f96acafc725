namespace Barret.Web.Server.Features.Shared.Enums;

/// <summary>
/// Defines the clear button display mode for input components.
/// </summary>
public enum ClearButtonDisplayMode
{
    /// <summary>
    /// Never show the clear button.
    /// </summary>
    Never,
    
    /// <summary>
    /// Automatically show the clear button when there is content.
    /// </summary>
    Auto,
    
    /// <summary>
    /// Always show the clear button.
    /// </summary>
    Always
}

/// <summary>
/// Defines the size mode for components.
/// </summary>
public enum ComponentSizeMode
{
    /// <summary>
    /// Small size.
    /// </summary>
    Small,
    
    /// <summary>
    /// Medium size (default).
    /// </summary>
    Medium,
    
    /// <summary>
    /// Large size.
    /// </summary>
    Large
}

/// <summary>
/// Defines the render style for buttons.
/// </summary>
public enum ButtonRenderStyle
{
    /// <summary>
    /// Primary button style.
    /// </summary>
    Primary,
    
    /// <summary>
    /// Secondary button style.
    /// </summary>
    Secondary,
    
    /// <summary>
    /// Danger button style.
    /// </summary>
    Danger,
    
    /// <summary>
    /// Success button style.
    /// </summary>
    Success,
    
    /// <summary>
    /// Warning button style.
    /// </summary>
    Warning,
    
    /// <summary>
    /// Info button style.
    /// </summary>
    Info,
    
    /// <summary>
    /// Light button style.
    /// </summary>
    Light,
    
    /// <summary>
    /// Dark button style.
    /// </summary>
    Dark,
    
    /// <summary>
    /// Link button style.
    /// </summary>
    Link
}

/// <summary>
/// Defines the render style mode for buttons.
/// </summary>
public enum ButtonRenderStyleMode
{
    /// <summary>
    /// Contained button style (filled).
    /// </summary>
    Contained,
    
    /// <summary>
    /// Outlined button style.
    /// </summary>
    Outlined,
    
    /// <summary>
    /// Text button style.
    /// </summary>
    Text
}

/// <summary>
/// Defines the icon position for buttons.
/// </summary>
public enum ButtonIconPosition
{
    /// <summary>
    /// Icon appears before the text.
    /// </summary>
    BeforeText,
    
    /// <summary>
    /// Icon appears after the text.
    /// </summary>
    AfterText
}
